{"name": "mogic-frontend-starter", "version": "1.0.0", "description": "This starter template should give all Mogicneers a kind of template for simple frontend projects", "main": "index.js", "repository": "********************:frontend/starter.git", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "scripts": {"lint": "yarn eslint src/**"}, "dependencies": {"typescript": "^5.8.3"}, "devDependencies": {"@eslint/css": "^0.6.0", "@eslint/js": "^9.24.0", "eslint": "^9.24.0", "eslint-config-prettier": "^10.1.2", "globals": "^16.0.0", "prettier": "3.5.3", "typescript-eslint": "^8.30.1"}}