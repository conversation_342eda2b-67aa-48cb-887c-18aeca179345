#!/bin/sh
FILES=$(git diff --cached --name-only --diff-filter=ACMR | sed 's| |\\ |g')
[ -z "$FILES" ] && exit 0

# check all selected files
echo "$FILES" | xargs yarn run lint --no-warn-ignored

if [ $? -eq 0 ]
then
  echo "\033[1;32mAll Files are checked. No Lint errors\033[0m"
  exit 0
else
  echo "\033[1;31mAll Files are checked. some Lint errors.\033[0m\nPlease check the listed files or errors"
  exit 1
fi
