init-git-hooks:
	@for file in ./template/githooks/*; do \
	  	target="./.git/hooks/$$(basename $$file)"; \
	  	if [ -f "$$target" ]; then \
	  		if cmp -s "$$file" "$$target"; then \
	  			echo "File $$target already exists with the same content. Ignoring."; \
	  		else \
	  			echo "Warning: File $$target exists with different content. Renaming to $$target.old"; \
	  			mv "$$target" "$$target.old"; \
	  			cat "$$file" >> "$$target"; \
	  			echo "moved $$(basename $$file)"; \
	  		fi; \
	  	else \
	  		cat "$$file" >> "$$target"; \
	  		echo "moved $$(basename $$file)"; \
	  	fi; \
		chmod +x "$$target"; \
	done

checkstyle:
	yarn run lint
