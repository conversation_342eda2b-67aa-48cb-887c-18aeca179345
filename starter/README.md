# Mogic Frontend Starter

This starter template should give all Mogicneers a kind of template for simple frontend projects. 

It contains a standard Vite Config with editor configuration and Lint as well as formatting tools


## Getting started

```
cd <ProjectsFolder>
<NAME_EMAIL>:frontend/starter.git mogic-frontend-starter
```

## Update and further development

### Setup and Editor Config 

#### NVM - Node Version Manager

To define a Node Environment based on your Project Setup you should have defined a [`.nvmrc` File](.nvmrc)
more information on the github repo of the [Node Version Manager](https://github.com/nvm-sh/nvm)

maybe you should use the `nvm use` cli command or you have a [deeper shell integration](https://github.com/nvm-sh/nvm?tab=readme-ov-file#calling-nvm-use-automatically-in-a-directory-with-a-nvmrc-file)

#### Editor Config File

https://editorconfig.org/#file-format-details

### Linting

#### CSS/Style Lint

#### JS/TS Lint

### Formatting

#### Prettier

## Authors and acknowledgment

## License

## Project status

- [ ] Predefining
